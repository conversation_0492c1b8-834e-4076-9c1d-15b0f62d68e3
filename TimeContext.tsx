import React, { createContext, useContext, useState, ReactNode } from 'react';

interface TimeContextType {
  simulatedTime: Date | null;
  setSimulatedTime: (time: Date | null) => void;
  getCurrentTime: () => Date;
}

const TimeContext = createContext<TimeContextType | undefined>(undefined);

export const useTime = () => {
  const context = useContext(TimeContext);
  if (context === undefined) {
    throw new Error('useTime must be used within a TimeProvider');
  }
  return context;
};

interface TimeProviderProps {
  children: ReactNode;
}

export const TimeProvider: React.FC<TimeProviderProps> = ({ children }) => {
  const [simulatedTime, setSimulatedTime] = useState<Date | null>(null);

  const getCurrentTime = (): Date => {
    return simulatedTime || new Date();
  };

  return (
    <TimeContext.Provider value={{ simulatedTime, setSimulatedTime, getCurrentTime }}>
      {children}
    </TimeContext.Provider>
  );
};
