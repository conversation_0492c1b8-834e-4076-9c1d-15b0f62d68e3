import React from 'react';
import { useTime } from './TimeContext';

const TimeSimulator: React.FC = () => {
  const { simulatedTime, setSimulatedTime } = useTime();

  const simulateTime = (hour: number, minute: number) => {
    const now = new Date();
    const simulated = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, minute, 0);
    setSimulatedTime(simulated);

    // Arrêter la simulation après 10 secondes
    setTimeout(() => {
      setSimulatedTime(null);
    }, 10000);
  };

  const stopSimulation = () => {
    setSimulatedTime(null);
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white">
      <h3 className="text-lg font-bold mb-3">Simulateur de Temps</h3>
      
      {simulatedTime && (
        <div className="mb-3 p-2 bg-blue-600/50 rounded">
          <p className="text-sm">Simulation active:</p>
          <p className="font-mono">{simulatedTime.toLocaleTimeString()}</p>
          <button 
            onClick={stopSimulation}
            className="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm"
          >
            Arrêter
          </button>
        </div>
      )}

      <div className="grid grid-cols-3 gap-2">
        <button 
          onClick={() => simulateTime(6, 0)}
          className="px-3 py-2 bg-orange-600 hover:bg-orange-700 rounded text-sm"
        >
          6h00
        </button>
        <button 
          onClick={() => simulateTime(12, 0)}
          className="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded text-sm"
        >
          12h00
        </button>
        <button 
          onClick={() => simulateTime(18, 0)}
          className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm"
        >
          18h00
        </button>
        <button 
          onClick={() => simulateTime(0, 0)}
          className="px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded text-sm"
        >
          00h00
        </button>
        <button 
          onClick={() => simulateTime(3, 0)}
          className="px-3 py-2 bg-indigo-600 hover:bg-indigo-700 rounded text-sm"
        >
          3h00
        </button>
        <button 
          onClick={() => simulateTime(21, 0)}
          className="px-3 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm"
        >
          21h00
        </button>
      </div>
    </div>
  );
};

export default TimeSimulator;
