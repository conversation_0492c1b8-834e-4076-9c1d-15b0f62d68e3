import React, { useEffect, useState } from 'react';
import * as SunCalc from 'suncalc';
import { useTime } from './TimeContext';

interface DebugInfo {
  currentTime: string;
  sunPosition: {
    altitude: number;
    azimuth: number;
  };
  sunTimes: {
    sunrise: string;
    sunset: string;
    solarNoon: string;
    nadir: string;
  };
  moonPosition: {
    altitude: number;
    azimuth: number;
    phase: number;
    illumination: number;
  };
  phase: string;
  currentColors: {
    sky: string;
    horizon: string;
  };
}

const DebugPanel: React.FC = () => {
  const { getCurrentTime, simulatedTime } = useTime();
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Coordonnées par défaut (Paris)
  const latitude = 48.8566;
  const longitude = 2.3522;

  const updateDebugInfo = () => {
    const currentTime = getCurrentTime();
    
    // Calculs solaires
    const sunPosition = SunCalc.getPosition(currentTime, latitude, longitude);
    const sunTimes = SunCalc.getTimes(currentTime, latitude, longitude);
    
    // Calculs lunaires
    const moonPosition = SunCalc.getMoonPosition(currentTime, latitude, longitude);
    const moonIllumination = SunCalc.getMoonIllumination(currentTime);
    
    // Déterminer la phase
    const sunAltitude = sunPosition.altitude * (180 / Math.PI);
    let phase = '';
    if (sunAltitude > 6) {
      phase = 'Jour';
    } else if (sunAltitude > -6) {
      phase = 'Crépuscule';
    } else if (sunAltitude > -12) {
      phase = 'Crépuscule nautique';
    } else if (sunAltitude > -18) {
      phase = 'Crépuscule astronomique';
    } else {
      phase = 'Nuit';
    }

    // Couleurs simplifiées
    const currentColors = {
      sky: phase === 'Jour' ? '#87CEEB' : phase === 'Nuit' ? '#191970' : '#FF6347',
      horizon: phase === 'Jour' ? '#FFE4B5' : '#2F4F4F'
    };

    setDebugInfo({
      currentTime: currentTime.toLocaleString(),
      sunPosition: {
        altitude: sunAltitude,
        azimuth: sunPosition.azimuth * (180 / Math.PI)
      },
      sunTimes: {
        sunrise: sunTimes.sunrise.toLocaleTimeString(),
        sunset: sunTimes.sunset.toLocaleTimeString(),
        solarNoon: sunTimes.solarNoon.toLocaleTimeString(),
        nadir: sunTimes.nadir.toLocaleTimeString()
      },
      moonPosition: {
        altitude: moonPosition.altitude * (180 / Math.PI),
        azimuth: moonPosition.azimuth * (180 / Math.PI),
        phase: moonIllumination.phase,
        illumination: moonIllumination.fraction
      },
      phase: phase,
      currentColors: currentColors
    });
  };

  useEffect(() => {
    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (simulatedTime) {
      updateDebugInfo();
    }
  }, [simulatedTime]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-2 text-white text-sm hover:bg-black/90"
      >
        Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/90 backdrop-blur-sm rounded-lg p-4 text-white text-xs max-w-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold">Debug Panel</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      {debugInfo && (
        <div className="space-y-2">
          <div>
            <strong>Heure:</strong> {debugInfo.currentTime}
            {simulatedTime && <span className="text-yellow-400"> (Simulé)</span>}
          </div>
          
          <div>
            <strong>Phase:</strong> {debugInfo.phase}
          </div>
          
          <div>
            <strong>Soleil:</strong>
            <div className="ml-2">
              Altitude: {debugInfo.sunPosition.altitude.toFixed(1)}°<br/>
              Azimuth: {debugInfo.sunPosition.azimuth.toFixed(1)}°
            </div>
          </div>
          
          <div>
            <strong>Horaires:</strong>
            <div className="ml-2">
              Lever: {debugInfo.sunTimes.sunrise}<br/>
              Coucher: {debugInfo.sunTimes.sunset}
            </div>
          </div>
          
          <div>
            <strong>Lune:</strong>
            <div className="ml-2">
              Altitude: {debugInfo.moonPosition.altitude.toFixed(1)}°<br/>
              Phase: {(debugInfo.moonPosition.phase * 100).toFixed(0)}%<br/>
              Illumination: {(debugInfo.moonPosition.illumination * 100).toFixed(0)}%
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugPanel;
